import type { Env } from "@/api/types";
import { initSupabaseAdmin } from "./supabase";

/**
 * Check if a user has a specific role on a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param requiredRole The required role ('owner' or 'member')
 * @param env Environment variables
 * @returns Promise<boolean> - true if user has the required role or higher
 */
export async function hasTeamRole(
  userId: string,
  teamId: string,
  requiredRole: 'owner' | 'member',
  env: Env
): Promise<boolean> {
  try {
    // Use service role client to bypass RLS
    const supabaseAdmin = initSupabaseAdmin(env);

    // Query the team_users table directly
    const { data: teamUser, error } = await supabaseAdmin
      .from('team_users')
      .select('team_role')
      .eq('user_id', userId)
      .eq('team_id', teamId)
      .single();

    if (error || !teamUser) {
      console.log(`User ${userId} is not a member of team ${teamId}`);
      return false;
    }

    // Check if user has the required role
    const userRole = teamUser.team_role;
    
    if (requiredRole === 'member') {
      // Any role (owner or member) satisfies member requirement
      return userRole === 'owner' || userRole === 'member';
    } else if (requiredRole === 'owner') {
      // Only owner role satisfies owner requirement
      return userRole === 'owner';
    }

    return false;
  } catch (error) {
    console.error('Error checking team role:', error);
    return false;
  }
}

/**
 * Check if a user is an owner of a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param env Environment variables
 * @returns Promise<boolean> - true if user is an owner of the team
 */
export async function isTeamOwner(
  userId: string,
  teamId: string,
  env: Env
): Promise<boolean> {
  return hasTeamRole(userId, teamId, 'owner', env);
}

/**
 * Check if a user is a member (owner or member) of a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param env Environment variables
 * @returns Promise<boolean> - true if user is a member of the team
 */
export async function isTeamMember(
  userId: string,
  teamId: string,
  env: Env
): Promise<boolean> {
  return hasTeamRole(userId, teamId, 'member', env);
}

/**
 * Get a user's role on a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param env Environment variables
 * @returns Promise<'owner' | 'member' | null> - the user's role or null if not a member
 */
export async function getUserTeamRole(
  userId: string,
  teamId: string,
  env: Env
): Promise<'owner' | 'member' | null> {
  try {
    // Use service role client to bypass RLS
    const supabaseAdmin = initSupabaseAdmin(env);

    // Query the team_users table directly
    const { data: teamUser, error } = await supabaseAdmin
      .from('team_users')
      .select('team_role')
      .eq('user_id', userId)
      .eq('team_id', teamId)
      .single();

    if (error || !teamUser) {
      return null;
    }

    return teamUser.team_role as 'owner' | 'member';
  } catch (error) {
    console.error('Error getting user team role:', error);
    return null;
  }
}
