import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import type { Context } from "hono";

interface AcceptTeamInviteRequest {
  token: string;
}

// POST /team-invites/accept
export const handleAcceptTeamInvite = async (c: Context<{ Bindings: Env }>) => {
  try {
    // Verify authentication for this endpoint
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    const body = await c.req.json() as AcceptTeamInviteRequest;
    const { token } = body;

    // Validate required fields
    if (!token) {
      return c.json({ error: "Missing required field: token" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client with user JWT
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Find the invitation by token and validate it's still active (within 24 hours)
    const { data: invitation, error: inviteError } = await supabaseClient
      .from('team_invites')
      .select(`
        id,
        team_id,
        email,
        team_role,
        team_name,
        created_at,
        invited_by_user_id
      `)
      .eq('token', token)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Only invitations from last 24 hours
      .single();

    if (inviteError || !invitation) {
      return c.json({ error: "Invitation not found or expired" }, 404);
    }

    // Check if user is already a member of the team
    const { data: existingMember, error: memberCheckError } = await supabaseClient
      .from('team_users')
      .select('team_role')
      .eq('team_id', invitation.team_id)
      .eq('user_id', auth.userId)
      .single();

    if (!memberCheckError && existingMember) {
      return c.json({ error: "You are already a member of this team" }, 400);
    }

    // Get team information for response
    const { data: team, error: teamError } = await supabaseClient
      .from('team_accounts')
      .select('slug')
      .eq('id', invitation.team_id)
      .single();

    if (teamError || !team) {
      return c.json({ error: "Team not found" }, 404);
    }

    // Start a transaction-like operation by adding user to team
    const { data: newTeamUser, error: addUserError } = await supabaseClient
      .from('team_users')
      .insert({
        team_id: invitation.team_id,
        user_id: auth.userId,
        team_role: invitation.team_role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (addUserError) {
      console.error("Error adding user to team:", addUserError);
      
      // Check if it's a unique constraint violation (user already exists)
      if (addUserError.code === '23505') {
        return c.json({ error: "You are already a member of this team" }, 400);
      }
      
      return c.json({
        error: "Failed to add user to team",
        details: addUserError.message
      }, 500);
    }

    // Delete the invitation after successful acceptance
    const { error: deleteError } = await supabaseClient
      .from('team_invites')
      .delete()
      .eq('token', token);

    if (deleteError) {
      console.error("Error deleting invitation after acceptance:", deleteError);
      // Don't fail the request since the user was successfully added to the team
      // Just log the error for monitoring
    }

    return c.json({
      success: true,
      team_id: invitation.team_id,
      team_role: invitation.team_role,
      team_name: invitation.team_name,
      slug: team.slug,
      message: "Successfully joined the team"
    });

  } catch (error) {
    console.error("Error accepting team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to accept team invitation"
    }, 500);
  }
};
