import type { Env } from "@/api/types";
import { initSupabaseAdmin } from "@/api/utils/supabase";
import type { Context } from "hono";

// GET /team-invites/lookup/:token
export const handleLookupTeamInvite = async (c: Context<{ Bindings: Env }>) => {
  try {
    // Get token from URL params
    const { token } = c.req.param();

    if (!token) {
      return c.json({ error: "Token is required" }, 400);
    }

    // Use service role client since this is a public lookup (no user authentication required)
    const supabaseAdmin = initSupabaseAdmin(c.env);

    // Find the invitation by token and validate it's still active (within 24 hours)
    const { data: invitation, error: inviteError } = await supabaseAdmin
      .from('team_invites')
      .select(`
        id,
        team_id,
        email,
        team_role,
        team_name,
        created_at,
        invited_by_user_id
      `)
      .eq('token', token)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Only invitations from last 24 hours
      .single();

    if (inviteError || !invitation) {
      return c.json({
        active: false,
        error: "Invitation not found or expired"
      }, 404);
    }

    // Get additional team information
    const { data: team, error: teamError } = await supabaseAdmin
      .from('team_accounts')
      .select('slug, public_metadata')
      .eq('id', invitation.team_id)
      .single();

    if (teamError || !team) {
      return c.json({
        active: false,
        error: "Team not found"
      }, 404);
    }

    // Get inviter information (optional, for display purposes)
    const { data: inviter, error: inviterError } = await supabaseAdmin
      .from('user_accounts')
      .select('first_name, last_name, email')
      .eq('user_id', invitation.invited_by_user_id)
      .single();

    // Don't fail if inviter info is not found, just continue without it
    const inviterInfo = inviterError ? null : inviter;

    return c.json({
      active: true,
      invitation: {
        team_name: invitation.team_name,
        team_role: invitation.team_role,
        email: invitation.email,
        created_at: invitation.created_at,
        team_slug: team.slug,
        invited_by: inviterInfo ? {
          name: `${inviterInfo.first_name || ''} ${inviterInfo.last_name || ''}`.trim() || 'Unknown',
          email: inviterInfo.email
        } : null
      }
    });

  } catch (error) {
    console.error("Error looking up team invitation:", error);
    return c.json({
      active: false,
      error: error instanceof Error ? error.message : "Failed to lookup team invitation"
    }, 500);
  }
};
