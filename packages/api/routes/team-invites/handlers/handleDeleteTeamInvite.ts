import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import type { Context } from "hono";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// DELETE /team-invites/:inviteId
export const handleDeleteTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get invite ID from URL params
    const { inviteId } = c.req.param();

    if (!inviteId) {
      return c.json({ error: "Invite ID is required" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client with user JWT
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // First, get the invitation to check if it exists and get the team_id
    const { data: invitation, error: inviteError } = await supabaseClient
      .from('team_invites')
      .select('id, team_id, email, team_name')
      .eq('id', inviteId)
      .single();

    if (inviteError || !invitation) {
      return c.json({ error: "Invitation not found" }, 404);
    }

    // Check if user is owner of the team
    const { data: userRole, error: roleError } = await supabaseClient
      .from('team_users')
      .select('team_role')
      .eq('team_id', invitation.team_id)
      .eq('user_id', auth.userId)
      .single();

    if (roleError || !userRole || userRole.team_role !== 'owner') {
      return c.json({ error: "Only team owners can delete invitations" }, 403);
    }

    // Delete the invitation
    const { error: deleteError } = await supabaseClient
      .from('team_invites')
      .delete()
      .eq('id', inviteId);

    if (deleteError) {
      console.error("Error deleting team invitation:", deleteError);
      return c.json({
        error: "Failed to delete team invitation",
        details: deleteError.message
      }, 500);
    }

    return c.json({
      success: true,
      message: "Team invitation deleted successfully",
      deleted_invitation: {
        id: invitation.id,
        email: invitation.email,
        team_name: invitation.team_name
      }
    });

  } catch (error) {
    console.error("Error deleting team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to delete team invitation"
    }, 500);
  }
};
