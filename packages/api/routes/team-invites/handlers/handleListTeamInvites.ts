import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { createUnauthorizedResponse } from "../../../utils/auth";
import { isTeamOwner } from "../../../utils/teamRoleCheck";
import { teamInvites } from "../../../schema/billing";
import { eq, gte, desc } from "drizzle-orm";
import type { Context } from "hono";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// GET /team-invites/:teamId
export const handleListTeamInvites = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get team ID from URL params
    const { teamId } = c.req.param();

    if (!teamId) {
      return c.json({ error: "Team ID is required" }, 400);
    }

    // Check if user is owner of the team
    console.log("Checking team ownership for user:", auth.userId, "team:", teamId);

    const isOwner = await isTeamOwner(auth.userId, teamId, c.env);

    if (!isOwner) {
      console.log("Access denied - user is not team owner:", { userId: auth.userId, teamId });
      return c.json({ error: "Only team owners can view team invitations" }, 403);
    }

    console.log("User is team owner, proceeding with invitation list");

    // Use Drizzle ORM with direct database connection (same as billing endpoint)
    const db = createDbClient(c.env);

    // Get query parameters for pagination
    const url = new URL(c.req.url);
    const limit = parseInt(url.searchParams.get('limit') || '25');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Validate pagination parameters
    if (limit < 1 || limit > 100) {
      return c.json({ error: "Limit must be between 1 and 100" }, 400);
    }

    if (offset < 0) {
      return c.json({ error: "Offset must be non-negative" }, 400);
    }

    // Calculate 24 hours ago
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Get team invitations for the team (only active ones - created within 24 hours)
    const invitations = await db
      .select({
        id: teamInvites.id,
        email: teamInvites.email,
        teamRole: teamInvites.teamRole,
        teamName: teamInvites.teamName,
        createdAt: teamInvites.createdAt,
        updatedAt: teamInvites.updatedAt,
        invitedByUserId: teamInvites.invitedByUserId,
      })
      .from(teamInvites)
      .where(
        eq(teamInvites.teamId, teamId)
      )
      .orderBy(desc(teamInvites.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination info
    const totalCountResult = await db
      .select({ count: teamInvites.id })
      .from(teamInvites)
      .where(
        eq(teamInvites.teamId, teamId)
      );

    const totalCount = totalCountResult.length;

    // Format the response
    const formattedInvitations = invitations.map(invite => ({
      invitation_id: invite.id,
      email: invite.email,
      team_role: invite.teamRole,
      team_name: invite.teamName,
      created_at: invite.createdAt,
      updated_at: invite.updatedAt,
      invited_by_user_id: invite.invitedByUserId
    }));

    return c.json({
      success: true,
      invitations: formattedInvitations,
      pagination: {
        limit,
        offset,
        total: totalCount,
        has_more: totalCount > offset + limit
      }
    });

  } catch (error) {
    console.error("Error listing team invitations:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to list team invitations"
    }, 500);
  }
};
