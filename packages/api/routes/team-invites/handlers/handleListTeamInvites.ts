import type { Env } from "@/api/types";
import { initSupabaseAdmin } from "@/api/utils/supabase";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import { isTeamOwner } from "@/api/utils/teamRoleCheck";
import type { Context } from "hono";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// GET /team-invites/:teamId
export const handleListTeamInvites = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get team ID from URL params
    const { teamId } = c.req.param();

    if (!teamId) {
      return c.json({ error: "Team ID is required" }, 400);
    }

    // Check if user is owner of the team
    console.log("Checking team ownership for user:", auth.userId, "team:", teamId);

    const isOwner = await isTeamOwner(auth.userId, teamId, c.env);

    if (!isOwner) {
      console.log("Access denied - user is not team owner:", { userId: auth.userId, teamId });
      return c.json({ error: "Only team owners can view team invitations" }, 403);
    }

    console.log("User is team owner, proceeding with invitation list");

    // Use service role client to bypass RLS
    const supabaseAdmin = initSupabaseAdmin(c.env);

    // Get query parameters for pagination
    const url = new URL(c.req.url);
    const limit = parseInt(url.searchParams.get('limit') || '25');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Validate pagination parameters
    if (limit < 1 || limit > 100) {
      return c.json({ error: "Limit must be between 1 and 100" }, 400);
    }

    if (offset < 0) {
      return c.json({ error: "Offset must be non-negative" }, 400);
    }

    // Get team invitations for the team (only active ones - created within 24 hours)
    const { data: invitations, error: invitesError } = await supabaseAdmin
      .from('team_invites')
      .select(`
        id,
        email,
        team_role,
        team_name,
        created_at,
        updated_at,
        invited_by_user_id
      `)
      .eq('team_id', teamId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Only invitations from last 24 hours
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (invitesError) {
      console.error("Error fetching team invitations:", invitesError);
      return c.json({
        error: "Failed to fetch team invitations",
        details: invitesError.message
      }, 500);
    }

    // Get total count for pagination info
    const { count, error: countError } = await supabaseAdmin
      .from('team_invites')
      .select('*', { count: 'exact', head: true })
      .eq('team_id', teamId)
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (countError) {
      console.error("Error counting team invitations:", countError);
      // Continue without count info rather than failing
    }

    // Format the response
    const formattedInvitations = (invitations || []).map(invite => ({
      invitation_id: invite.id,
      email: invite.email,
      team_role: invite.team_role,
      team_name: invite.team_name,
      created_at: invite.created_at,
      updated_at: invite.updated_at,
      invited_by_user_id: invite.invited_by_user_id
    }));

    return c.json({
      success: true,
      invitations: formattedInvitations,
      pagination: {
        limit,
        offset,
        total: count || 0,
        has_more: (count || 0) > offset + limit
      }
    });

  } catch (error) {
    console.error("Error listing team invitations:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to list team invitations"
    }, 500);
  }
};
