import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse } from "@/api/utils/auth";
import type { Context } from "hono";

interface CreateTeamInviteRequest {
  team_id: string;
  email: string;
  team_role?: 'owner' | 'member';
}

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// POST /team-invites
export const handleCreateTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    const body = await c.req.json() as CreateTeamInviteRequest;
    const { team_id, email, team_role = 'member' } = body;

    // Validate required fields
    if (!team_id || !email) {
      return c.json({ error: "Missing required fields: team_id, email" }, 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return c.json({ error: "Invalid email format" }, 400);
    }

    // Validate team_role
    if (!['owner', 'member'].includes(team_role)) {
      return c.json({ error: "Invalid team_role. Must be 'owner' or 'member'" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client with user JWT
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Check if user is owner of the team
    const { data: userRole, error: roleError } = await supabaseClient
      .from('team_users')
      .select('team_role')
      .eq('team_id', team_id)
      .eq('user_id', auth.userId)
      .single();

    if (roleError || !userRole || userRole.team_role !== 'owner') {
      return c.json({ error: "Only team owners can create invitations" }, 403);
    }

    // Check if team exists and get team name
    const { data: team, error: teamError } = await supabaseClient
      .from('team_accounts')
      .select('name')
      .eq('id', team_id)
      .single();

    if (teamError || !team) {
      return c.json({ error: "Team not found" }, 404);
    }

    // Check if user is already a member of the team
    const { data: existingMember, error: memberCheckError } = await supabaseClient
      .from('team_users')
      .select('user_id')
      .eq('team_id', team_id)
      .eq('user_id', auth.userId)
      .single();

    // If there's no error, it means the user is already a member
    if (!memberCheckError && existingMember) {
      return c.json({ error: "User is already a member of this team" }, 400);
    }

    // Check if there's already a pending invitation for this email and team
    const { data: existingInvite, error: inviteCheckError } = await supabaseClient
      .from('team_invites')
      .select('id')
      .eq('team_id', team_id)
      .eq('email', email)
      .single();

    if (!inviteCheckError && existingInvite) {
      return c.json({ error: "An invitation for this email already exists for this team" }, 400);
    }

    // Generate a unique token for the invitation
    const token = generateInviteToken();

    // Create the team invitation
    const { data: newInvite, error: createError } = await supabaseClient
      .from('team_invites')
      .insert({
        team_id,
        email,
        team_role,
        token,
        invited_by_user_id: auth.userId,
        team_name: team.name,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      console.error("Error creating team invitation:", createError);
      return c.json({
        error: "Failed to create team invitation",
        details: createError.message
      }, 500);
    }

    // TODO: Send invitation email here
    // This would typically involve calling an email service like SendGrid, Resend, etc.
    console.log(`TODO: Send invitation email to ${email} for team ${team.name} with token ${token}`);

    return c.json({
      success: true,
      invitation: {
        id: newInvite.id,
        team_id: newInvite.team_id,
        email: newInvite.email,
        team_role: newInvite.team_role,
        team_name: newInvite.team_name,
        token: newInvite.token,
        created_at: newInvite.created_at
      }
    });

  } catch (error) {
    console.error("Error creating team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to create team invitation"
    }, 500);
  }
};

// Helper function to generate a secure random token
function generateInviteToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 30; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
