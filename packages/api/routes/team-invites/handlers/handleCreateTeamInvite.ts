import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { createUnauthorizedResponse } from "../../../utils/auth";
import { isTeamOwner } from "../../../utils/teamRoleCheck";
import { teamAccounts, teamInvites } from "../../../schema/billing";
import { eq, and, sql } from "drizzle-orm";
import type { Context } from "hono";

interface CreateTeamInviteRequest {
  team_id: string;
  email: string;
  team_role?: 'owner' | 'member';
}

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// POST /team-invites
export const handleCreateTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    console.log("DEBUG: auth object =", JSON.stringify(auth, null, 2));

    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    const body = await c.req.json() as CreateTeamInviteRequest;
    const { team_id, email, team_role = 'member' } = body;

    // Validate required fields
    if (!team_id || !email) {
      return c.json({ error: "Missing required fields: team_id, email" }, 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return c.json({ error: "Invalid email format" }, 400);
    }

    // Validate team_role
    if (!['owner', 'member'].includes(team_role)) {
      return c.json({ error: "Invalid team_role. Must be 'owner' or 'member'" }, 400);
    }

    // Check if user is owner of the team
    const isOwner = await isTeamOwner(auth.userId, team_id, c.env);

    if (!isOwner) {
      return c.json({ error: "Only team owners can create invitations" }, 403);
    }

    // Use Drizzle ORM with direct database connection (same as billing endpoint)
    const db = createDbClient(c.env);

    // Check if team exists and get team name
    const teamResult = await db
      .select({
        name: teamAccounts.name,
      })
      .from(teamAccounts)
      .where(eq(teamAccounts.id, team_id))
      .limit(1);

    if (teamResult.length === 0) {
      return c.json({ error: "Team not found" }, 404);
    }

    const team = teamResult[0];

    // Check if the email being invited is already associated with a user who is a member of the team
    // We need to join with auth.users to check if the email is already associated with a user
    // For now, we'll skip this check and rely on the invitation duplicate check below
    // TODO: Add a proper check to see if the email is already associated with a team member

    // Check if there's already a pending invitation for this email and team
    const existingInviteResult = await db
      .select({
        id: teamInvites.id,
      })
      .from(teamInvites)
      .where(and(
        eq(teamInvites.teamId, team_id),
        eq(teamInvites.email, email)
      ))
      .limit(1);

    // If there's a result, it means there's already a pending invitation
    if (existingInviteResult.length > 0) {
      return c.json({ error: "An invitation for this email already exists for this team" }, 400);
    }

    // Generate a unique token for the invitation
    const token = generateInviteToken();

    // Debug: Check auth.userId value
    console.log("DEBUG: auth.userId =", auth.userId, "type:", typeof auth.userId);

    // Create the team invitation using raw SQL to bypass Drizzle field mapping issue
    const inviteId = crypto.randomUUID();
    const now = new Date().toISOString();

    const newInviteResult = await db.execute(sql`
      INSERT INTO basejump.team_invites (
        id, team_role, team_id, token, invited_by_user_id, team_name, email, updated_at, created_at
      ) VALUES (
        ${inviteId}, ${team_role}, ${team_id}, ${token}, ${auth.userId}, ${team.name}, ${email}, ${now}, ${now}
      ) RETURNING id, team_id, email, team_role, team_name, token, created_at, updated_at, invited_by_user_id
    `);

    if (newInviteResult.length === 0) {
      console.error("Error creating team invitation: No result returned");
      return c.json({
        error: "Failed to create team invitation"
      }, 500);
    }

    const newInvite = newInviteResult[0];

    // TODO: Send invitation email here
    // This would typically involve calling an email service like SendGrid, Resend, etc.
    console.log(`TODO: Send invitation email to ${email} for team ${team.name} with token ${token}`);

    return c.json({
      success: true,
      invitation: {
        id: newInvite.id,
        team_id: newInvite.teamId,
        email: newInvite.email,
        team_role: newInvite.teamRole,
        team_name: newInvite.teamName,
        token: newInvite.token,
        created_at: newInvite.createdAt
      }
    });

  } catch (error) {
    console.error("Error creating team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to create team invitation"
    }, 500);
  }
};

// Helper function to generate a secure random token
function generateInviteToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 30; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
