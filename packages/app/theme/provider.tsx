import type React from "react";
import { createContext, useContext } from "react";
import { View, type ViewStyle } from "react-native";
import { useColorScheme } from "nativewind";
import { type ColorScheme, colorSchemes } from "./colorSchemes";

const ThemeColorsContext = createContext<{ [key: string]: string } | null>(null);

export const useThemeColor = ({ color, opacity = 1 }: { color: string; opacity?: number }) => {
  const colorTokens = useContext(ThemeColorsContext);
  if (!colorTokens) {
    throw new Error("useThemeColors must be used within a ThemeProvider");
  }

  const colorValue = colorTokens[color];
  if (!colorValue) {
    throw new Error(`Invalid color value: ${color}`);
  }

  return `rgba(${colorValue}, ${opacity})`;
};

export const ThemeProvider = (props: {
  colorScheme: ColorScheme;
  children: React.ReactNode;
}) => {
  const { colorScheme: systemColorScheme } = useColorScheme();

  const lightOrDarkMode = systemColorScheme || "light";

  const themeStyle: ViewStyle = colorSchemes[props.colorScheme][lightOrDarkMode];

  const hookTokens = Object.keys(themeStyle)
    .filter((key) => key.startsWith("--color-"))
    .reduce(
      (acc, key) => {
        const newKey = key.replace("--color-", "");
        acc[newKey] = (themeStyle as Record<string, string>)[key];
        return acc;
      },
      {} as Record<string, string>,
    );

  return (
    <ThemeColorsContext.Provider value={hookTokens as Record<string, string>}>
      <View style={[{ flex: 1 }, themeStyle]}>{props.children}</View>
    </ThemeColorsContext.Provider>
  );
};

export default ThemeProvider;
