import React, { useState } from "react";
import { ActivityIndicator } from "react-native";
import {
  Box,
  Text,
  Button,
  Input,
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicatorWrapper,
  SelectDragIndicator,
  SelectItem,
  Heading,
} from "@/components/ui";
import SentIcon from '@platform/assets/icons/solid/sent.svg'
// import MailAccountIcon from '@platform/assets/icons/mail-account.svg'
import { useCreateInvitation } from "@/hooks/mutations/useCreateInvitation";

interface InviteTeamMemberProps {
  teamId: string;
  onInviteCreated: (token: string) => void;
}

export function InviteTeamMember({ teamId, onInviteCreated }: InviteTeamMemberProps) {
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<"member" | "owner">("member");

  const createInvitationMutation = useCreateInvitation();

  const handleInvite = async () => {
    if (!teamId || !inviteEmail.trim()) {
      alert("Por favor, insira um email válido.");
      return;
    }

    try {
      const result = await createInvitationMutation.mutateAsync({
        teamId,
        role: inviteRole,
        invitationType: "24_hour",
      });

      console.log("Invitation created with result:", result);

      if (result.token) {
        console.log("Setting generated token to:", result.token);
        onInviteCreated(result.token);
        setInviteEmail("");
        alert("Convite criado com sucesso!");
      } else {
        console.error("Token missing from result!", result);
        alert("Erro ao criar convite: Token não encontrado.");
      }
    } catch (err: unknown) {
      console.error("Failed to create invite:", err instanceof Error ? err.message : String(err));
      alert("Erro ao criar convite: " + (err instanceof Error ? err.message : String(err)));
    }
  };

  return (
    <Box>
      <Heading>
        Enviar convite
      </Heading>
      <Text className="text-sm text-text-tertiary my-1">
        Digite o email e selecione a função do colaborador que deseja convidar
      </Text>
      <Box className="flex-row mt-4">
        <Box className="flex-1 bg-background">
          <Input
            fullWidth
            placeholder="Exemplo: <EMAIL>"
            value={inviteEmail}
            onChangeText={setInviteEmail}
            className="rounded-r-none border-r-0"
            keyboardType="email-address"
            autoCapitalize="none"
          // beforeIcon={MailAccountIcon}
          />
        </Box>
        <Select
          selectedValue={inviteRole}
          onValueChange={(value) => setInviteRole(value as "member" | "owner")}
        >
          <SelectTrigger className="w-32 rounded-l-none">
            <SelectInput
              placeholder="Função"
              value={inviteRole === "member" ? "Membro" : "Gestor"}
            />
            <SelectIcon size="xl" className="text-text-tertiary" />
          </SelectTrigger>
          <SelectPortal>
            <SelectBackdrop />
            <SelectContent>
              <SelectDragIndicatorWrapper>
                <SelectDragIndicator />
              </SelectDragIndicatorWrapper>
              <SelectItem label="Membro" value="member" />
              <SelectItem label="Gestor" value="owner" />
            </SelectContent>
          </SelectPortal>
        </Select>
        <Button
          variant="secondary"
          onPress={handleInvite}
          disabled={createInvitationMutation.isPending || !inviteEmail.trim()}
          className="ml-4"
          size="md"
          isLoading={createInvitationMutation.isPending}
          loadingMessage="Enviando convite..."
        >
          <Text>Convidar</Text>
          <SentIcon className="text-text ml-2 w-4 h-4 rotate-45" />
        </Button>
      </Box>
    </Box>
  );
}