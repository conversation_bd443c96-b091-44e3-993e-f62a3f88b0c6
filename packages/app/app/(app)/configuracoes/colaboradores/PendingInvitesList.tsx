import React, { useState } from "react";
import { FlatList, Pressable, ActivityIndicator, Alert, Platform } from "react-native";
import {
  Box,
  Text,
  Badge,
  Center,
  Heading,
  Button,
  Input,
} from "@/components/ui";
import { useTeamInvitations } from "@/hooks/useTeamInvitations";
import { useDeleteInvitation } from "@/hooks/mutations/useDeleteInvitation";

import * as Clipboard from "expo-clipboard";
import Modal from "@/components/Modal";
import ViewIcon from "@/assets/icons/view.svg";

interface PendingInvitesListProps {
  teamId: string;
}

export function PendingInvitesList({ teamId }: PendingInvitesListProps) {
  const [selectedInvitation, setSelectedInvitation] = useState<any | null>(null);
  const deleteInvitationMutation = useDeleteInvitation();

  // Define baseUrl based on platform
  let baseUrl = "https://app.imoblr.com.br"; // Default/Fallback URL (e.g., production)
  if (Platform.OS === "web") {
    // Ensure window is defined (it should be on web)
    if (typeof window !== "undefined") {
      baseUrl = window.location.origin;
    }
  } else {
    // TODO: Define base URL for native platforms if different (e.g., using env vars or specific deep link setup)
  }

  const {
    data: invitationsData,
    isLoading: isLoadingInvitations,
    error: errorInvitations,
  } = useTeamInvitations(teamId);

  const handleInvitationPress = (item: any) => {
    console.log("Showing invitation details for:", item.invitation_id);
    setSelectedInvitation(item);
  };

  const handleDeleteInvitation = (invitationId: string) => {
    if (!teamId) return;
    console.log("Showing delete confirmation for invitation:", invitationId, "team:", teamId);

    // For web platform or platforms where Alert might not work as expected
    if (Platform.OS === "web") {
      if (confirm("Tem certeza que deseja remover este convite pendente?")) {
        deleteInvitation(invitationId, teamId);
      }
    } else {
      // For native platforms
      Alert.alert("Remover Convite", "Tem certeza que deseja remover este convite pendente?", [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Remover",
          style: "destructive",
          onPress: () => deleteInvitation(invitationId, teamId),
        },
      ]);
    }
  };

  // Separate function to handle the actual deletion
  const deleteInvitation = async (invitationId: string, teamId: string) => {
    console.log("Executing deletion for invitation:", invitationId);
    try {
      await deleteInvitationMutation.mutateAsync({ invitationId, teamId });
      setSelectedInvitation(null); // Clear the selected invitation after deletion
      console.log("Invitation deleted successfully");
      alert("Convite removido com sucesso!"); // Simple feedback for now
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error("Failed to delete invitation:", errorMessage);
      alert(`Erro ao remover convite: ${errorMessage}`);
    }
  };

  if (isLoadingInvitations) {
    return (
      <Center className="py-4">
        <ActivityIndicator size="large" />
        <Text className="mt-2">Carregando convites...</Text>
      </Center>
    );
  }

  if (errorInvitations) {
    return (
      <Text className="text-destructive py-4">
        Erro ao carregar convites: {errorInvitations.message}
      </Text>
    );
  }

  if (!invitationsData || invitationsData.length === 0) {
    return null;
  }

  return (
    <>
      <Text className="mb-3 font-medium text-foreground text-lg">Convites pendentes</Text>
      <FlatList
        data={invitationsData}
        keyExtractor={(item) => item.invitation_id}
        renderItem={({ item }) => (
          <Pressable
            className="group mb-2 flex-row items-center justify-between rounded-md border border-border bg-background p-3 shadow-sm"
            onPress={() => handleInvitationPress(item)}
          >
            <Box className="flex-1">
              <Text className="font-medium text-foreground text-sm">{item.email}</Text>
              <Box className="flex-row items-center mt-1">
                <Text className="text-muted-foreground text-xs">Função:</Text>
                <Badge
                  className="ml-2 w-fit rounded-full bg-primary-50"
                  variant={item.team_role === "owner" ? "primary-outline" : "outline"}
                  size="xs"
                >
                  <Text className="capitalize">{item.team_role === "owner" ? "Gestor" : "Colaborador"}</Text>
                </Badge>
              </Box>
              <Text className="mt-0.5 font-medium text-text-tertiary text-xs">
                Criado em {new Date(item.created_at).toLocaleDateString()}
              </Text>
            </Box>
            <Center className="pointer-events-none ml-2 h-6 w-6 rounded-full bg-background-darkest opacity-0 group-hover:opacity-100">
              <ViewIcon className="h-4 w-4 text-text-secondary" />
            </Center>
          </Pressable>
        )}
      />

      {/* Invitation Details Modal */}
      <Modal
        isOpen={!!selectedInvitation}
        onClose={() => setSelectedInvitation(null)}
        size="sm"
      >
        <Box className="p-6">
          <Heading size="md" className="mb-4 text-foreground">
            Detalhes do Convite
          </Heading>
          {selectedInvitation && (
            <>
              <Text className="mb-2 text-muted-foreground">Email:</Text>
              <Input value={selectedInvitation.email} readOnly={true} className="mb-4" />

              <Text className="mb-2 text-muted-foreground">Função:</Text>
              <Input
                value={selectedInvitation.team_role === "owner" ? "Gestor" : "Colaborador"}
                readOnly={true}
                className="mb-4"
              />

              <Text className="mb-2 text-muted-foreground">Criado em:</Text>
              <Input
                value={new Date(selectedInvitation.created_at).toLocaleString()}
                readOnly={true}
                className="mb-4"
              />

              <Text className="mb-4 text-sm text-muted-foreground">
                O convite foi enviado por email para {selectedInvitation.email}.
                O link de convite é válido por 24 horas a partir da criação.
              </Text>
            </>
          )}

          <Button
            variant="outline"
            className="mt-4"
            onPress={() => setSelectedInvitation(null)}
          >
            <Text>Fechar</Text>
          </Button>

          <Button
            variant="destructive-link"
            onPress={() => {
              if (selectedInvitation && teamId) {
                console.log("Deleting invitation:", selectedInvitation.invitation_id, "from team:", teamId);
                handleDeleteInvitation(selectedInvitation.invitation_id);
              } else {
                console.error("Cannot delete invitation: missing invitation or teamId", { selectedInvitation, teamId });
              }
            }}
          >
            <Text>Cancelar convite</Text>
          </Button>
        </Box>
      </Modal>
    </>
  );
}