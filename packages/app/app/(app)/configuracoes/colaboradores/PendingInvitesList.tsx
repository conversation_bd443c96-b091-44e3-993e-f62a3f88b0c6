import React, { useState } from "react";
import { FlatList, Pressable, ActivityIndicator, Alert, Platform } from "react-native";
import {
  Box,
  Text,
  Badge,
  Center,
  Heading,
  Button,
  Input,
} from "@/components/ui";
import { useTeamInvitations } from "@/hooks/useTeamInvitations";
import { useDeleteInvitation } from "@/hooks/mutations/useDeleteInvitation";
import { useSupabase } from "@/hooks/useSupabase";
import * as Clipboard from "expo-clipboard";
import Modal from "@/components/Modal";
import ViewIcon from "@/assets/icons/view.svg";

interface PendingInvitesListProps {
  teamId: string;
}

export function PendingInvitesList({ teamId }: PendingInvitesListProps) {
  const [isLoadingToken, setIsLoadingToken] = useState(false);
  const [generatedToken, setGeneratedToken] = useState<string | null>(null);
  const [currentInvitationId, setCurrentInvitationId] = useState<string | null>(null);
  const supabaseClient = useSupabase();
  const deleteInvitationMutation = useDeleteInvitation();

  // Define baseUrl based on platform
  let baseUrl = "https://app.imoblr.com.br"; // Default/Fallback URL (e.g., production)
  if (Platform.OS === "web") {
    // Ensure window is defined (it should be on web)
    if (typeof window !== "undefined") {
      baseUrl = window.location.origin;
    }
  } else {
    // TODO: Define base URL for native platforms if different (e.g., using env vars or specific deep link setup)
  }
  
  const {
    data: invitationsData,
    isLoading: isLoadingInvitations,
    error: errorInvitations,
  } = useTeamInvitations(teamId);

  const handleInvitationPress = async (item: any) => {
    setIsLoadingToken(true);
    try {
      console.log("Getting token for invitation ID:", item.invitation_id);

      // Let's use our new get_invitation_token RPC function
      const { data: rpcData, error: rpcError } = await supabaseClient.rpc("get_invitation_token", {
        invitation_id: item.invitation_id,
      });

      console.log("RPC lookup_invitation result:", { rpcData, rpcError });

      if (rpcError) {
        console.error("RPC error:", rpcError);
        throw rpcError;
      }

      if (!rpcData) {
        console.error("No data returned from lookup_invitation");
        throw new Error("Convite não encontrado");
      }

      // Parse the JSON result if needed
      const invitationData = typeof rpcData === "string" ? JSON.parse(rpcData) : rpcData;
      console.log("Parsed invitation data:", invitationData);

      // Check if we have a token field in the response
      if (invitationData.token) {
        console.log(`Found token: ${invitationData.token}`);
        setGeneratedToken(invitationData.token);
        setCurrentInvitationId(item.invitation_id);
      } else if (invitationData.id) {
        // If we only have the ID, try a direct table query as fallback
        const { data, error } = await supabaseClient
          .from("invitations")
          .select("token")
          .eq("id", item.invitation_id)
          .single();

        if (error || !data?.token) {
          throw new Error("Token não encontrado para este convite");
        }

        console.log(`Found token via direct query: ${data.token}`);
        setGeneratedToken(data.token);
        setCurrentInvitationId(item.invitation_id);
      } else {
        console.error("No token found in invitation data");
        throw new Error("Dados do convite incompletos");
      }
    } catch (err) {
      console.error("Error fetching invitation token:", err);
      alert("Erro ao obter detalhes do convite");
    } finally {
      setIsLoadingToken(false);
    }
  };

  const handleDeleteInvitation = (invitationId: string) => {
    if (!teamId) return;
    console.log("Showing delete confirmation for invitation:", invitationId, "team:", teamId);

    // For web platform or platforms where Alert might not work as expected
    if (Platform.OS === "web") {
      if (confirm("Tem certeza que deseja remover este convite pendente?")) {
        deleteInvitation(invitationId, teamId);
      }
    } else {
      // For native platforms
      Alert.alert("Remover Convite", "Tem certeza que deseja remover este convite pendente?", [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Remover",
          style: "destructive",
          onPress: () => deleteInvitation(invitationId, teamId),
        },
      ]);
    }
  };

  // Separate function to handle the actual deletion
  const deleteInvitation = async (invitationId: string, teamId: string) => {
    console.log("Executing deletion for invitation:", invitationId);
    try {
      await deleteInvitationMutation.mutateAsync({ invitationId, teamId });
      setGeneratedToken(null); // Clear the token after deletion
      setCurrentInvitationId(null); // Clear the invitation ID
      console.log("Invitation deleted successfully");
      alert("Convite removido com sucesso!"); // Simple feedback for now
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error("Failed to delete invitation:", errorMessage);
      alert(`Erro ao remover convite: ${errorMessage}`);
    }
  };

  if (isLoadingInvitations) {
    return (
      <Center className="py-4">
        <ActivityIndicator size="large" />
        <Text className="mt-2">Carregando convites...</Text>
      </Center>
    );
  }

  if (errorInvitations) {
    return (
      <Text className="text-destructive py-4">
        Erro ao carregar convites: {errorInvitations.message}
      </Text>
    );
  }

  if (!invitationsData || invitationsData.length === 0) {
    return null;
  }

  return (
    <>
      <Text className="mb-3 font-medium text-foreground text-lg">Convites pendentes</Text>
      <FlatList
        data={invitationsData}
        keyExtractor={(item) => item.invitation_id}
        renderItem={({ item }) => (
          <Pressable
            className="group mb-2 flex-row items-center justify-between rounded-md border border-border bg-background p-3 shadow-sm"
            onPress={() => handleInvitationPress(item)}
            disabled={isLoadingToken}
          >
            <Box className="flex-1">
              <Box className="flex-row items-center">
                <Text className="text-muted-foreground text-xs">Tipo:</Text>
                <Badge
                  className="ml-2 w-fit rounded-full bg-primary-50"
                  variant={item.account_role === "owner" ? "primary-outline" : "outline"}
                  size="xs"
                >
                  <Text className="capitalize">{item.account_role === "owner" ? "Gestor" : "Colaborador"}</Text>
                </Badge>
              </Box>
              <Text className="mt-0.5 font-medium text-text-tertiary text-xs">
                Criado em {new Date(item.created_at).toLocaleDateString()}
              </Text>
            </Box>
            <Center className="pointer-events-none ml-2 h-6 w-6 rounded-full bg-background-darkest opacity-0 group-hover:opacity-100">
              {isLoadingToken ? (
                <ActivityIndicator size="small" />
              ) : (
                <ViewIcon className="h-4 w-4 text-text-secondary" />
              )}
            </Center>
          </Pressable>
        )}
      />

      {/* Token Display Modal - only for viewing existing invitation tokens */}
      <Modal
        isOpen={!!generatedToken}
        onClose={() => {
          setGeneratedToken(null);
          setCurrentInvitationId(null);
        }}
        size="sm"
      >
        <Box className="p-6">
          <Heading size="md" className="mb-4 text-foreground">
            Detalhes do Convite
          </Heading>
          <>
            <Text className="mb-2 text-muted-foreground">Link de convite (válido por 24h):</Text>
            <Input value={`${baseUrl}/aceitar-convite?token=${generatedToken}`} readOnly={true} className="mb-4" />
            <Button
              onPress={async () => {
                const inviteLink = `${baseUrl}/aceitar-convite?token=${generatedToken}`;
                await Clipboard.setStringAsync(inviteLink);
                console.log("Invite link copied with token:", generatedToken);
                alert("Link copiado para a área de transferência!");
              }}
            >
              <Text>Copiar Link</Text>
            </Button>
          </>
          <Button
            variant="outline"
            className="mt-4"
            onPress={() => {
              setGeneratedToken(null);
              setCurrentInvitationId(null);
            }}
          >
            <Text>Fechar</Text>
          </Button>
          <Button
            variant="destructive-link"
            onPress={() => {
              if (currentInvitationId && teamId) {
                console.log("Deleting invitation:", currentInvitationId, "from team:", teamId);
                handleDeleteInvitation(currentInvitationId);
              } else {
                console.error("Cannot delete invitation: missing ID or teamId", { currentInvitationId, teamId });
              }
            }}
          >
            <Text>Cancelar convite</Text>
          </Button>
        </Box>
      </Modal>
    </>
  );
}