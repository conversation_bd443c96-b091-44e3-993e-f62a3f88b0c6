import AddIcon from "@/assets/icons/add.svg";
import BuildingIcon from "@/assets/icons/building.svg";
// import HousesIllustration from "@/assets/illustrations/houses-amico-storyset.svg";
import HousesIllustration from "@/assets/illustrations/storyset/beach house-rafiki.svg";
import { ChartCard } from "@/components/ChartCard";
import { EmptyPagePlaceholder } from "@/components/EmptyPagePlaceholder";
import PageLayout, { PageRow } from "@/components/PageLayout";
import PropertiesList from "@/components/PropertiesList";
import { Box, Button, Text } from "@/components/ui";
import { useDrawers } from "@/context/drawer";
import { usePropertiesList } from "@/hooks";
import { useThemeColor } from "@/theme";
import { useState } from "react";
import { LineChart } from "react-gifted-charts";
import { useWindowDimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

const barData = [
  {
    value: 124,
    label: "Jan",
    topLabelComponent: () => <Text>0.7</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 900,
    label: "Fev",
    topLabelComponent: () => <Text>0.8</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 1312,
    label: "Mar",
    topLabelComponent: () => <Text>0.6</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 3032,
    label: "Abr",
    topLabelComponent: () => <Text>0.4</Text>,
    labelComponent: () => <Text className="" />,
  },
  {
    value: 6050,
    label: "Mai",
    topLabelComponent: () => <Text>0.9</Text>,
    labelComponent: () => <Text className="" />,
    frontColor: "rgba(0, 0, 0, 0.5)",
  },
  {
    value: 12959,
    label: "Mai",
    topLabelComponent: () => <Text>0.9</Text>,
    labelComponent: () => <Text className="" />,
    frontColor: "rgba(0, 0, 0, 0.5)",
  },
];

export default function Home() {
  const { width } = useWindowDimensions();
  const { data: properties, isLoading, error } = usePropertiesList();
  const chartStrokeColor = useThemeColor({ color: "primary", opacity: 0.25 });
  const chartGradientColor = useThemeColor({ color: "primary", opacity: 0.2 });

  const { openCreatePropertyDrawer } = useDrawers();
  const [chartWidth, setChartWidth] = useState(0);
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);

  const handlePropertySelect = (propertyId: string) => {
    setSelectedProperties((prev) =>
      prev.includes(propertyId) ? prev.filter((id) => id !== propertyId) : [...prev, propertyId],
    );
  };

  return (
    <PageLayout
      pageTitle="Imóveis"
      description="Gerencie os seus imóveis, monitore estatísticas e movimentações."
      pageIcon={BuildingIcon}
      searchLabel="Procurar imovel..."
      actions={[
        <Button size="sm" key="add-property-header-action" onPress={openCreatePropertyDrawer}>
          <AddIcon className="mr-2 w-[16px] text-text-inverse" />
          <Text>Cadastrar imovel</Text>
        </Button>,
      ]}
    >
      {properties && properties.length > 0 ? (
        <>
          {/* <PageRow>
            <Box className="w-full flex-row gap-2 md:gap-4 lg:gap-6 xl:gap-8">
              <ChartCard
                value="259"
                change={{ type: "up", value: "+33%", description: "nos últimos 30 dias" }}
                title="Total de imoveis"
                className="flex-1"
              >
                <Box className="-ml-2 translate-y-4">
                  <LineChart
                    curved={false}
                    areaChart
                    data={barData}
                    hideYAxisText
                    yAxisThickness={0}
                    xAxisThickness={0}
                    hideRules
                    thickness={2}
                    color={chartStrokeColor}
                    startFillColor={chartGradientColor}
                    startOpacity={0.5}
                    endFillColor="rgba(255, 255, 255, 0)"
                    endOpacity={0}
                    isAnimated
                    animationDuration={1500}
                    animateTogether
                    animateOnDataChange
                    dataPointsColor={chartStrokeColor}
                    dataPointsWidth={1}
                    dataPointsHeight={1}
                    hideDataPoints={false}
                    width={chartWidth - 12}
                    initialSpacing={4}
                    endSpacing={4}
                    height={40}
                    adjustToWidth={true}
                    yAxisExtraHeight={6}
                    disableScroll
                    showDataPointLabelOnFocus
                    hideOrigin
                    yAxisOffset={-80}
                  />
                </Box>
              </ChartCard>
              <ChartCard
                value="12"
                change={{ type: "up", value: "+33%", description: "nos últimos 30 dias" }}
                title="Prospectados"
                className="flex-1"
              >
                <Box
                  className="-ml-2 translate-y-4"
                  onLayout={(event) => {
                    const { width } = event.nativeEvent.layout;
                    setChartWidth(width);
                  }}
                >
                  <LineChart
                    curved={false}
                    areaChart
                    data={barData}
                    hideYAxisText
                    yAxisThickness={0}
                    xAxisThickness={0}
                    hideRules
                    thickness={2}
                    color={chartStrokeColor}
                    startFillColor={chartGradientColor}
                    startOpacity={0.5}
                    endFillColor="rgba(255, 255, 255, 0)"
                    endOpacity={0}
                    isAnimated
                    animationDuration={1500}
                    animateTogether
                    animateOnDataChange
                    dataPointsColor={chartStrokeColor}
                    dataPointsWidth={1}
                    dataPointsHeight={1}
                    hideDataPoints={false}
                    width={chartWidth - 12}
                    initialSpacing={4}
                    endSpacing={4}
                    height={40}
                    adjustToWidth={true}
                    yAxisExtraHeight={6}
                    disableScroll
                    showDataPointLabelOnFocus
                    hideOrigin
                    yAxisOffset={-80}
                  />
                </Box>
              </ChartCard>
              <ChartCard
                value="1"
                change={{ type: "up", value: "+33%", description: "nos últimos 30 dias" }}
                title="Negócios fechados"
                className="flex-1"
              >
                <Box className="-ml-2 translate-y-4">
                  <LineChart
                    curved={false}
                    areaChart
                    data={barData}
                    hideYAxisText
                    yAxisThickness={0}
                    xAxisThickness={0}
                    hideRules
                    thickness={2}
                    color={chartStrokeColor}
                    startFillColor={chartGradientColor}
                    startOpacity={0.5}
                    endFillColor="rgba(255, 255, 255, 0)"
                    endOpacity={0}
                    isAnimated
                    animationDuration={1500}
                    animateTogether
                    animateOnDataChange
                    dataPointsColor={chartStrokeColor}
                    dataPointsWidth={1}
                    dataPointsHeight={1}
                    hideDataPoints={false}
                    width={chartWidth - 12}
                    initialSpacing={4}
                    endSpacing={4}
                    height={40}
                    adjustToWidth={true}
                    yAxisExtraHeight={6}
                    disableScroll
                    showDataPointLabelOnFocus
                    hideOrigin
                    yAxisOffset={-80}
                  />
                </Box>
              </ChartCard>
            </Box>
          </PageRow> */}
          <PropertiesList />
        </>
      ) : (
        <EmptyPagePlaceholder
          illustration={HousesIllustration}
          title="Nenhum imóvel cadastrado"
          description="Clique no botão abaixo para adicionar o seu primeiro imóvel"
          button={{
            label: "Adicionar um imóvel",
            icon: "add",
            position: "before",
            onPress: openCreatePropertyDrawer,
          }}
        />
      )}
    </PageLayout>
  );
}
