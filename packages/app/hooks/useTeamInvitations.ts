import { useSupabase } from "@/hooks/useSupabase";
import { useQuery } from "@tanstack/react-query";
import type { GetAccountInvitesResponse } from "@/types/basejump_shared"; // Renamed type for clarity

export const TEAM_INVITATIONS_QUERY_KEY = "team_invitations";

// Define a more specific type based on the actual RPC response structure if needed
// For now, using the generated GetAccountInvitesResponse
// The RPC `get_team_invitations` returns JSON objects with: account_role, created_at, invitation_type, invitation_id
type TeamInvitation = {
  account_role: "owner" | "member";
  created_at: string; // Assuming ISO string format
  email: string;
  invitation_type: "one_time" | "24_hour";
  invitation_id: string;
};

export function useTeamInvitations(teamId: string | undefined) {
  const supabaseClient = useSupabase();

  return useQuery<TeamInvitation[], Error>({
    // Pass single options object
    queryKey: [TEAM_INVITATIONS_QUERY_KEY, teamId], // Query key includes teamId
    queryFn: async () => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }
      if (!teamId) {
        // Return empty array or handle as needed if teamId is not available yet
        // Relies on the `enabled` option below
        return [];
      }

      // Assuming default limit/offset for now, adjust if pagination is needed
      const { data, error } = await supabaseClient.rpc("get_team_invitations", {
        team_id: teamId,
      });

      if (error) {
        console.error("Error fetching team invitations:", error);
        throw new Error(error.message || "Failed to fetch team invitations");
      }

      // Ensure data is not null/undefined before returning
      return (data as TeamInvitation[] | null) ?? [];
    },
    // Options are part of the same object
    // Only run the query if teamId is available
    enabled: !!teamId,
    // Optional: Configure staleTime, cacheTime, etc.
    // staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
