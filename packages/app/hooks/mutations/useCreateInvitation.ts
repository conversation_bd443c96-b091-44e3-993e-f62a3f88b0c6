import { useSupabase } from "@/hooks/useSupabase";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TEAM_INVITATIONS_QUERY_KEY } from "@/hooks/useTeamInvitations";

type CreateInvitationVariables = {
  teamId: string;
  role: "member" | "owner";
  invitationType: "one_time" | "24_hour";
};

type CreateInvitationResponse = {
  token: string;
  invitation_id: string;
};

export function useCreateInvitation() {
  const supabaseClient = useSupabase();
  const queryClient = useQueryClient();

  return useMutation<CreateInvitationResponse, Error, CreateInvitationVariables>({
    mutationFn: async ({ teamId, role, invitationType }: CreateInvitationVariables) => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }
      if (!teamId) {
        throw new Error("Team ID is required");
      }
      if (!role) {
        throw new Error("Role is required");
      }
      if (!invitationType) {
        throw new Error("Invitation type is required");
      }

      const { data, error } = await supabaseClient.rpc("create_invitation", {
        team_id: teamId,
        account_role: role,
        invitation_type: invitationType,
      });

      if (error) {
        console.error("Error creating invitation:", error);
        throw new Error(error.message || "Failed to create invitation");
      }

      if (!data) {
        throw new Error("No data returned from create_invitation");
      }

      // The RPC function returns a JSON object with token and invitation_id
      const result = typeof data === "string" ? JSON.parse(data) : data;
      
      if (!result.token) {
        throw new Error("No token returned from create_invitation");
      }

      return {
        token: result.token,
        invitation_id: result.invitation_id || result.id,
      };
    },
    onSuccess: (_, variables) => {
      // Invalidate the invitations query to refetch the list
      queryClient.invalidateQueries({
        queryKey: [TEAM_INVITATIONS_QUERY_KEY, variables.teamId],
      });
      console.log("Invitation created successfully");
    },
    onError: (error: Error) => {
      console.error("Mutation error creating invitation:", error.message);
    },
  });
}