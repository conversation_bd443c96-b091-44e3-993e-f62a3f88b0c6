import { useSupabase } from "@/hooks/useSupabase";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TEAM_INVITATIONS_QUERY_KEY } from "@/hooks/useTeamInvitations";

type DeleteInvitationVariables = {
  invitationId: string;
  teamId: string; // Needed to invalidate the correct query
};

export function useDeleteInvitation() {
  const supabaseClient = useSupabase();
  const queryClient = useQueryClient();

  return useMutation<void, Error, DeleteInvitationVariables>({
    mutationFn: async ({ invitationId }: DeleteInvitationVariables) => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }
      if (!invitationId) {
        throw new Error("Invitation ID is required");
      }

      const { error } = await supabaseClient.rpc("delete_invitation", {
        invitation_id: invitationId,
      });

      if (error) {
        console.error("Error deleting invitation:", error);
        throw new Error(error.message || "Failed to delete invitation");
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate the invitations query to refetch the list
      queryClient.invalidateQueries({
        queryKey: [TEAM_INVITATIONS_QUERY_KEY, variables.teamId],
      });
      console.log("Invitation deleted successfully:", variables.invitationId);
      // TODO: Add user feedback (e.g., toast notification)
    },
    onError: (error: Error) => {
      console.error("Mutation error deleting invitation:", error.message);
      // TODO: Add user feedback (e.g., toast notification)
    },
  });
}
