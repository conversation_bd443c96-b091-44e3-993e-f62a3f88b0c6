import type React from "react";
import { type FC, useEffect, useRef, useState } from "react";
import { TouchableOpacity, View, type LayoutChangeEvent } from "react-native";
import Animated, { useSharedValue, useAnimatedStyle, withSpring } from "react-native-reanimated";
import { Box, Text } from "@/components/ui";
import { cx } from "@/utils";
import type { SvgProps } from "react-native-svg";

export interface TabItem {
  id: string;
  label: string;
  onPress: () => void;
  isActive: boolean;
  icon?: FC<SvgProps>;
  activeIcon?: FC<SvgProps>;
}

interface HorizontalTabsProps {
  tabItems: TabItem[];
  className?: string;
  variant?: "tabs" | "buttons";
}

const HorizontalTabs = ({ tabItems, className, variant = "tabs" }: HorizontalTabsProps) => {
  const [measurements, setMeasurements] = useState<Record<string, { x: number; width: number }>>({});
  const [layoutRefresh, setLayoutRefresh] = useState(0);
  const indicatorLeft = useSharedValue(0);
  const indicatorWidth = useSharedValue(0);

  useEffect(() => {
    setMeasurements({});
  }, []);

  useEffect(() => {
    const activeTab = tabItems.find((tab) => tab.isActive);
    if (activeTab) {
      if (!measurements[activeTab.id]) {
        setLayoutRefresh((prev) => prev + 1);
        return;
      }
      indicatorLeft.value = withSpring(measurements[activeTab.id].x, {
        damping: 15,
        stiffness: 180,
        mass: 0.6,
      });
      indicatorWidth.value = withSpring(measurements[activeTab.id].width, {
        damping: 15,
        stiffness: 180,
        mass: 0.6,
      });
    }
  }, [tabItems, measurements]);

  const indicatorStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: indicatorLeft.value }],
    width: indicatorWidth.value,
  }));

  const handleTabLayout = (id: string) => (e: LayoutChangeEvent) => {
    const { x, width } = e.nativeEvent.layout;
    setMeasurements((prev) => ({ ...prev, [id]: { x, width } }));
  };

  return (
    <Box
      className={cx(
        "relative overflow-hidden",
        {
          "rounded-xl border border-border-light bg-background-dark px-1": variant === "buttons",
          "border-b border-border-light": variant === "tabs",
        },
        className,
      )}
    >
      <View className="flex-row" onLayout={() => setLayoutRefresh((prev) => prev + 1)}>
        {tabItems.map((tab) => (
          <TouchableOpacity
            key={`${tab.id}-${layoutRefresh}`}
            onPress={tab.onPress}
            onLayout={handleTabLayout(tab.id)}
            className={cx("h-12 flex-row items-center justify-center gap-2", {
              "px-3": variant === "buttons",
              "px-5": variant === "tabs",
            })}
          >
            {tab.icon &&
              (tab.isActive && tab.activeIcon ? (
                <tab.activeIcon className="h-[18px] w-[18px] text-primary" />
              ) : (
                <tab.icon className="h-[18px] w-[18px] text-text-secondary" />
              ))}
            <Text className={cx("font-medium text-text-secondary", { "text-primary": tab.isActive })}>{tab.label}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {variant === "buttons" && (
        <Animated.View
          style={[
            {
              position: "absolute",
              top: 4,
              zIndex: -50,
              height: 32,
            },
            indicatorStyle,
          ]}
        >
          <Box className="h-full w-full rounded-lg border border-primary-100 bg-primary-50/50 shadow-sm" />
        </Animated.View>
      )}
      {variant === "tabs" && (
        <Animated.View
          style={[
            {
              position: "absolute",
              bottom: -1,
              height: 3,
            },
            indicatorStyle,
          ]}
        >
          <Box className="h-full w-full bg-primary" />
        </Animated.View>
      )}
    </Box>
  );
};

export default HorizontalTabs;
