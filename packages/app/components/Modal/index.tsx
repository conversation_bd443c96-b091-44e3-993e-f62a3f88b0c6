import { Portal, PortalHost } from "@gorhom/portal";
import type { VariantProps } from "class-variance-authority";
import { memo, useEffect, useState } from "react";
import { ScrollView, TouchableWithoutFeedback } from "react-native";
import Reanimated, { FadeIn, FadeOut, useAnimatedStyle, withSequence, withTiming } from "react-native-reanimated";
import { Box } from "../ui";
import { modalVariants } from "./style";

export type ModalProps = {
  children: React.ReactNode;
  isOpen?: boolean;
  onClose?: () => void;
  size?: VariantProps<typeof modalVariants>["size"];
  closeOnClickOutside?: boolean;
  closeOnEscapeKey?: boolean;
};

const Modal = ({
  children,
  isOpen = false,
  size,
  onClose,
  closeOnClickOutside = true,
  closeOnEscapeKey = true,
}: ModalProps) => {
  const [isVisible, setIsVisible] = useState(isOpen);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setIsAnimating(true);
    } else {
      setIsAnimating(false);
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 300); // Match the animation duration

      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  // Add effect to handle Escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscapeKey) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose?.();
      }
    };

    // Add event listener for web
    document.addEventListener("keydown", handleKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [isOpen, closeOnEscapeKey, onClose]);

  const modalStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isAnimating ? 1 : 0, { duration: 300 }),
      transform: [
        {
          scale: withTiming(isAnimating ? 1 : 0.9, { duration: 300 }),
        },
      ],
    };
  });

  const overlayStyle = useAnimatedStyle(() => {
    return {
      opacity: withTiming(isAnimating ? 1 : 0, { duration: 300 }),
    };
  });

  if (!isVisible) return null;

  return (
    <Portal>
      <TouchableWithoutFeedback
        onPress={() => {
          if (closeOnClickOutside) {
            onClose?.();
          }
        }}
      >
        <Box className="absolute top-0 right-0 z-50 flex h-screen w-screen items-center justify-center">
          <Reanimated.View
            style={{
              position: "absolute",
              top: 0,
              right: 0,
              zIndex: 10,
              height: "100%",
              width: "100%",
            }}
          >
            <Reanimated.View
              style={[
                {
                  position: "absolute",
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                },
                overlayStyle,
              ]}
            >
              <Box className="h-full w-full bg-background-inverse/90" />
            </Reanimated.View>
          </Reanimated.View>
          <TouchableWithoutFeedback>
            <Reanimated.View style={[{ zIndex: 20 }, modalStyle]}>
              <Box className={modalVariants({ size })}>
                <PortalHost name="modal-header" />
                {children}
                <PortalHost name="modal-footer" />
              </Box>
            </Reanimated.View>
          </TouchableWithoutFeedback>
        </Box>
      </TouchableWithoutFeedback>
    </Portal>
  );
};

export * from "./ModalFooter";
export * from "./ModalHeader";
export default memo(Modal);
