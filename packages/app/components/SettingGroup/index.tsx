import { Box, Heading, Text } from "@/components/ui";
import React from "react";

interface SettingGroupProps {
  title: string;
  description: string;
  children: React.ReactNode;
}

export const SettingGroup = ({ title, description, children }: SettingGroupProps) => {
  return (
    <Box className="flex-row border-b border-border-light p-16">
      <Box className="w-[400px] pr-4">
        <Heading size="lg">{title}</Heading>
        <Text className="mt-2 text-sm text-text-tertiary w-[80%]">{description}</Text>
      </Box>
      <Box className="flex-1 pl-4">{children}</Box>
    </Box>
  );
};