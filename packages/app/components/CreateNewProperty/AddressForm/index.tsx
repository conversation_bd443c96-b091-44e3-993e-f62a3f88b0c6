import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import MapIcon from "@/assets/icons/duotone/map-location.svg";
import LocationIcon from "@/assets/icons/location.svg";
import SearchingIcon from "@/assets/icons/searching.svg";
import LocationIllustration from "@/assets/illustrations/storyset/Address-amico.svg";
import { AddressAutocomplete } from "@/components/AddressAutocomplete";
import { DrawerFooter } from "@/components/Drawer";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import { SkeletonLoader } from "@/components/Skeleton";
import { Box, Button, Center, FormField, FormInput, FormSwitch, Heading, Label, Text } from "@/components/ui";
import { Separator } from "@/components/ui/separator";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { ActivityIndicator } from "react-native";
import { fetchPlaceDetails } from "./utils";

const AddressForm = ({
  onSubmit,
  onCancel,
  onAddressSearchStart,
  onAddressSearchEnd,
}: {
  onCancel?: () => void;
  onSubmit?: () => void;
  onAddressSearchStart?: () => void;
  onAddressSearchEnd?: () => void;
}) => {
  const { control, resetField, setValue, watch, trigger } = useFormContext();
  const [selectedAddressData, setSelectedAddressData] = useState();
  const [shouldShowComplement, setShouldShowComplement] = useState(false);
  const [isAddressDataLoading, setIsAddressDataLoading] = useState(false);
  const { session } = useSupabaseAuth();
  const selectedAddressId = watch("address_id");
  const selectedAddressGooglePlaceId = watch("google_maps_place_id");
  const addressComplement = watch("address_complement");

  const isAddressSelected = !!selectedAddressGooglePlaceId;

  useEffect(() => {
    if (!selectedAddressGooglePlaceId || !session?.access_token) return;
    fetchAddressDetails(selectedAddressGooglePlaceId);
    return undefined;
  }, [selectedAddressGooglePlaceId, session?.access_token]);

  const fetchAddressDetails = async (placeId: string) => {
    console.log("is fetching");
    console.log("placeId", placeId);
    console.log("session", session?.access_token);
    setIsAddressDataLoading(true);

    const addressDetails = await fetchPlaceDetails({ placeId, accessToken: session?.access_token });

    if (!addressDetails) return;

    setSelectedAddressData(addressDetails);
    setValue("address_id", addressDetails.id, {
      shouldTouch: true,
    });

    console.log("fetched", addressDetails);

    setIsAddressDataLoading(false);
  };

  const handleAddressSelected = async (formattedAddress: string, placeId: string) => {
    if (!placeId) return;
    setValue("google_maps_place_id", placeId);
  };

  const resetSelectedAddress = () => {
    setSelectedAddressData(undefined);
    resetField("address_id", {
      keepTouched: false,
      keepDirty: false,
      keepError: false,
    });
    resetField("google_maps_place_id", {
      keepTouched: false,
      keepDirty: false,
      keepError: false,
    });
    resetField("address_complement", {
      keepTouched: false,
      keepDirty: false,
      keepError: false,
    });
    setShouldShowComplement(false);
  };

  return (
    <Box>
      <DrawerHeader
        title="Localização do imóvel"
        description="Digite parte do endereço e completaremos os campos automaticamente"
        icon={MapIcon}
        featuredBackground={true}
      />
      <DrawerFooter
        tertiaryAction={{
          label: "Cancelar",
          onPress: () => {
            onCancel?.();
          },
        }}
        primaryAction={{
          iconAfter: ArrowRightIcon,
          disabled: !selectedAddressId,
          label: "Salvar endereço e continuar",
          onPress: async () => {
            onSubmit?.();
          },
        }}
      />
      {/* <Box
        style={{ height: 240 }}
        className="mx-4 my-16 items-center justify-center overflow-hidden rounded-2xl border border-border bg-[#ECE7E5] shadow-sm"
      >
        {shouldMountMap && !hideMap ? (
          <HybridMap showMarker={isAddressSelected} lat={mapLat} lon={mapLon} />
        ) : (
          <ActivityIndicator size="large" color={`rgb(${brand[700]})`} style={{ marginBottom: 40 }} />
        )}
      </Box> */}
      <Center className="mx-4 my-16">
        <LocationIllustration className="mb-4 h-auto w-1/3" />
        <Heading size="lg" className="mb-2">
          Endereço do imóvel
        </Heading>
        <Text className="w-1/2 text-center text-sm text-text-tertiary">
          Basta digitar o nome da rua e o número e processaremos o restante automaticamente
        </Text>
      </Center>

      {!isAddressSelected && (
        <AddressAutocomplete
          label="Digite o endereço do imóvel"
          onAddressSelected={handleAddressSelected}
          onAddressSearchStart={onAddressSearchStart}
          onAddressSearchEnd={onAddressSearchEnd}
        />
      )}
      {isAddressSelected && (
        <Box className="mb-8 gap-4 px-12">
          <Box className="flex-row items-center">
            <Label className="mr-4">Endereço selecionado</Label>
            <Button variant="outline" size="xs" className="w-fit px-3" onPress={resetSelectedAddress}>
              <SearchingIcon className="mr-2 w-4 text-text" />
              <Text className="text-sm">Trocar endereço</Text>
            </Button>
          </Box>
          <Box className="rounded-xl bg-background-darker">
            <Box className="flex-row gap-2">
              <Box className="flex-1 rounded-lg border-4 border-background-darker">
                <Box className="flex-row gap-1">
                  <Box className="mb-1 w-full flex-row items-center justify-center rounded border border-primary-50 bg-background p-4 shadow">
                    <Center className="mr-3 rounded-xl border border-primary-100 bg-primary-50/50 p-1.5">
                      {isAddressDataLoading ? (
                        <ActivityIndicator size="small" />
                      ) : (
                        <LocationIcon className="h-6 w-6 text-primary" />
                      )}
                    </Center>
                    {isAddressDataLoading ? (
                      <Box className="flex-1">
                        <SkeletonLoader width={"80%"} height={12} style={{ borderRadius: 4, marginBottom: 6 }} />
                        <SkeletonLoader width={"60%"} height={6} style={{ borderRadius: 4 }} />
                      </Box>
                    ) : (
                      <Box className="flex-1">
                        <Text className="flex-1 font-medium text-primary">
                          {selectedAddressData?.street?.name}, {selectedAddressData?.street_number}
                        </Text>
                        <Text className="flex-1 text-primary text-sm">
                          {selectedAddressData?.neighborhood?.name}, {selectedAddressData?.city?.name} -{" "}
                          {selectedAddressData?.state} - {selectedAddressData?.postcode}
                        </Text>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>
            </Box>
            <Box className="p-4">
              <FormSwitch
                label="Adicionar complemento (Apto, bloco, etc.)"
                value={shouldShowComplement || !!addressComplement}
                name="address_complement_toggle"
                size="sm"
                onBlur={() => {}}
                onChange={() => {
                  setShouldShowComplement(!shouldShowComplement);
                }}
              />

              {(shouldShowComplement || !!addressComplement) && (
                <>
                  <Separator className="my-4" />
                  <Box className="rounded-xl bg-background-darker">
                    <FormField
                      control={control}
                      name="address_complement"
                      render={({ field }) => (
                        <FormInput label="Complemento" placeholder="Exemplo: Apto 101, Bloco A." {...field} />
                      )}
                    />
                  </Box>
                </>
              )}
            </Box>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default AddressForm;
